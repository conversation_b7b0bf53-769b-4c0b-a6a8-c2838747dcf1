<template>
    <view class="container">
        <!-- 批次状态筛选 -->
        <view class="tabs-container">
            <!-- 使用 u-tabs 组件 -->
            <u-tabs v-if="false" :list="batchStatusTabs" :current="currentTabIndex" @change="onTabChange"
                :scrollable="false" lineColor="#007AFF" :activeStyle="{ color: '#007AFF' }"
                :inactiveStyle="{ color: '#666666' }"></u-tabs>

            <!-- 备用方案：自定义选项卡 -->
            <view class="custom-tabs">
                <view v-for="(tab, index) in batchStatusTabs" :key="index"
                    :class="['custom-tab', currentTabIndex === index ? 'active' : '']" @tap="onTabChange(index)">
                    {{ tab.name }}
                </view>
            </view>
        </view>

        <!-- 批次列表 -->
        <view class="media-list-container">
            <!-- 紧凑型批次列表项 -->
            <view v-for="(batch, index) in filteredBatches" :key="index" class="media-item batch-item"
                @click="viewBatchDetail(batch)">

                <!-- 批次封面缩略图 -->
                <view class="media-thumbnail batch-thumbnail">
                    <image v-if="batch.videos && batch.videos.length > 0 && batch.videos[0].cover"
                        :src="batch.videos[0].cover" mode="aspectFill" class="media-thumbnail-image"></image>
                    <view v-else class="no-cover-placeholder">
                        <u-icon name="photo" size="24" color="#ccc"></u-icon>
                    </view>
                    <u-tag :text="getBatchStatusText(batch)" :type="getBatchStatusType(batch)" size="mini"
                        class="batch-status"></u-tag>
                </view>

                <!-- 批次内容信息 -->
                <view class="media-content">
                    <view class="media-title batch-title">{{ batch.title }}</view>
                    <view class="batch-meta">
                        <text class="batch-id">{{ batch.batchId }}</text>
                        <text class="media-meta-separator">·</text>
                        <text class="batch-participants">{{ batch.participants }}人参与</text>
                        <text class="media-meta-separator">·</text>
                        <text class="batch-reward">{{ batch.totalReward }}元</text>
                    </view>
                    <view class="batch-date">
                        {{ formatDateRange(batch.startTime, batch.endTime) }}
                    </view>
                </view>

            </view>

            <!-- 空状态 -->
            <u-empty v-if="filteredBatches.length === 0" mode="list" :text="`暂无${getCurrentStatusLabel()}批次`"
                iconSize="120" textSize="16" marginTop="100">
                <u-button type="primary" text="创建批次" @click="createNewBatch" size="normal" shape="round"></u-button>
            </u-empty>
        </view>
    </view>
</template>

<script>

import { queryBatches } from "@/api/batch.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
    mixins: [mediaCommonMixin],
    data () {
        return {
            batchList: [],
            batchStatusTabs: [
                { name: '全部', value: 'all' },
                { name: '进行中', value: 'active' },
                { name: '未开始', value: 'pending' },
                { name: '已结束', value: 'ended' }
            ],
            currentBatchStatus: 'all',
            currentTabIndex: 0
        }
    },
    computed: {
        filteredBatches () {
            let result = this.batchList;

            // 按状态筛选
            if (this.currentBatchStatus !== 'all') {
                result = result.filter(batch => batch.status === this.currentBatchStatus);
            }

            return result;
        }
    },
    created () {
        this.loadAllBatches();
    },
    methods: {
        async loadAllBatches () {
            try {
                this.showLoading("加载批次中...");

                const response = await queryBatches({
                    page: 1,
                    pageSize: 1000
                });

                if (response.success && response.data) {
                    this.batchList = response.data.items.map(batch => ({
                        id: batch.id,
                        batchId: batch.batchId,
                        title: batch.title,
                        description: batch.description,
                        startTime: batch.startTime,
                        endTime: batch.endTime,
                        status: this.calculateBatchStatus(batch),
                        participants: batch.participants || 0,
                        totalReward: batch.totalReward || 0,
                        videos: batch.videos || [],
                        createdAt: batch.createdAt
                    }));
                } else {
                    console.error('获取批次列表失败:', response.message);
                    this.showError(response.message || "获取批次列表失败");
                    this.batchList = [];
                }

                this.hideLoading();
            } catch (error) {
                console.error('加载批次列表失败:', error);
                this.hideLoading();
                this.showError("加载失败");
                this.batchList = [];
            }
        },

        // 计算批次状态
        calculateBatchStatus (batch) {
            const now = new Date();
            const startTime = new Date(batch.startTime);
            const endTime = new Date(batch.endTime);

            if (now < startTime) {
                return 'pending'; // 未开始
            } else if (now > endTime) {
                return 'ended'; // 已结束
            } else {
                return 'active'; // 进行中
            }
        },

        // 选项卡切换
        onTabChange (index) {
            this.currentTabIndex = index;
            this.currentBatchStatus = this.batchStatusTabs[index].value;
        },

        // 格式化日期范围
        formatDateRange (startTime, endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);

            const formatDate = (date) => {
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const hour = date.getHours().toString().padStart(2, '0');
                const minute = date.getMinutes().toString().padStart(2, '0');
                return `${month}-${day} ${hour}:${minute}`;
            };

            return `${formatDate(start)} ~ ${formatDate(end)}`;
        },

        // 获取批次状态文本
        getBatchStatusText (batch) {
            const statusMap = {
                'pending': '未开始',
                'active': '进行中',
                'ended': '已结束',
                'paused': '已暂停'
            };
            return statusMap[batch.status] || '未知';
        },

        // 获取批次状态对应的 uview-plus 标签类型
        getBatchStatusType (batch) {
            // 首先检查是否已过期
            const now = new Date();
            const endTime = new Date(batch.endTime);
            const startTime = new Date(batch.startTime);

            if (now > endTime) {
                return 'error'; // 已结束
            }

            if (now < startTime) {
                return 'warning'; // 未开始
            }

            // 其他状态
            if (batch.status === 'paused') return 'warning';
            return 'success'; // 进行中
        },

        viewBatchDetail (batch) {
            if (!batch || !batch.id) {
                console.error('无效的批次数据，无法跳转');
                uni.showToast({
                    title: '无效的批次数据',
                    icon: 'none'
                });
                return;
            }
            const url = `/pages/admin/media/batch-detail?id=${batch.id}`;
            uni.navigateTo({
                url: url,
                fail: (err) => {
                    console.error('跳转到批次详情页失败:', err);
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    });
                }
            });
        },

        createNewBatch () {
            uni.showToast({
                title: '请先选择视频创建批次',
                icon: 'none'
            });
        },

        getCurrentStatusLabel () {
            const currentTab = this.batchStatusTabs[this.currentTabIndex];
            return currentTab.name === '全部' ? '' : currentTab.name;
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/index.scss';

.container {
    padding: 0;
    background-color: #f7f7f7;
}

/* 选项卡容器 */
.tabs-container {
    background-color: #fff;
    padding: 0 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

/* 自定义选项卡样式 */
.custom-tabs {
    display: flex;
    justify-content: space-around;
    padding: 20rpx 0;
}

.custom-tab {
    flex: 1;
    text-align: center;
    padding: 16rpx 0;
    font-size: 28rpx;
    color: #666666;
    position: relative;
    transition: all 0.3s ease;
}

.custom-tab.active {
    color: #007AFF;
    font-weight: 600;
}

.custom-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40rpx;
    height: 4rpx;
    background-color: #007AFF;
    border-radius: 2rpx;
}

/* 媒体列表容器 */
.media-list-container {
    padding: 20rpx;
    padding-top: 0;
}

/* 媒体项通用样式 */
.media-item {
    display: flex;
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.media-item:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 缩略图样式 */
.media-thumbnail {
    position: relative;
    border-radius: 12rpx;
    overflow: hidden;
    margin-right: 20rpx;
}

.batch-thumbnail {
    width: 140rpx;
    height: 100rpx;
}

.media-thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 状态标签 */
.batch-status {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
}

/* 内容区域 */
.media-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.media-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 12rpx;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.media-meta-separator {
    color: #ddd;
    margin: 0 8rpx;
}

/* 批次特有样式 */
.batch-title {
    color: #186BFF;
}

.batch-meta {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 8rpx;
}

.batch-id {
    font-size: 22rpx;
    color: #666;
    font-weight: 500;
}

.batch-participants,
.batch-reward {
    font-size: 22rpx;
    color: #999;
}

.batch-date {
    font-size: 20rpx;
    color: #ccc;
}

/* 无封面占位符 */
.no-cover-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
